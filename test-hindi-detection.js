// Test script for Hindi language detection
// Run with: node test-hindi-detection.js

// Language detection function (copied from API)
function detectLanguage(text) {
  // Hindi Unicode ranges: Devanagari (0900-097F)
  const hindiRegex = /[\u0900-\u097F]/;
  
  // Count Hindi vs English characters
  const hindiMatches = text.match(hindiRegex);
  const hindiCharCount = hindiMatches ? hindiMatches.length : 0;
  
  // English characters (basic Latin)
  const englishRegex = /[a-zA-Z]/g;
  const englishMatches = text.match(englishRegex);
  const englishCharCount = englishMatches ? englishMatches.length : 0;
  
  // If more than 30% of characters are Hindi, treat as Hindi
  const totalChars = hindiCharCount + englishCharCount;
  if (totalChars > 0 && (hindiCharCount / totalChars) > 0.3) {
    return 'hi-IN';
  }
  
  // Default to English
  return 'en-US';
}

// Test cases
const testCases = [
  {
    text: "रामायण एक प्राचीन भारतीय महाकाव्य है",
    expected: 'hi-IN',
    description: "Pure Hindi text"
  },
  {
    text: "The Great Gatsby is a classic American novel",
    expected: 'en-US',
    description: "Pure English text"
  },
  {
    text: "राम और सीता की कहानी बहुत प्रसिद्ध है",
    expected: 'hi-IN',
    description: "Hindi story content"
  },
  {
    text: "Modern Ramayana with राम और सीता",
    expected: 'hi-IN',
    description: "Mixed content with significant Hindi"
  },
  {
    text: "Book title with few हिंदी words",
    expected: 'en-US',
    description: "Mostly English with few Hindi words"
  },
  {
    text: "गीता का ज्ञान और philosophy",
    expected: 'hi-IN',
    description: "Hindi with English words"
  }
];

console.log('🧪 Testing Hindi Language Detection\n');

testCases.forEach((testCase, index) => {
  const result = detectLanguage(testCase.text);
  const passed = result === testCase.expected;
  
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`Text: "${testCase.text}"`);
  console.log(`Expected: ${testCase.expected}, Got: ${result}`);
  console.log(`Status: ${passed ? '✅ PASS' : '❌ FAIL'}`);
  console.log('---');
});

// Additional analysis
console.log('\n📊 Character Analysis Examples:\n');

const analysisExamples = [
  "रामायण की कहानी",
  "The Ramayana story",
  "राम और Sita की love story"
];

analysisExamples.forEach(text => {
  const hindiMatches = text.match(/[\u0900-\u097F]/g);
  const englishMatches = text.match(/[a-zA-Z]/g);
  const hindiCount = hindiMatches ? hindiMatches.length : 0;
  const englishCount = englishMatches ? englishMatches.length : 0;
  const total = hindiCount + englishCount;
  const hindiPercentage = total > 0 ? (hindiCount / total * 100).toFixed(1) : 0;
  
  console.log(`Text: "${text}"`);
  console.log(`Hindi chars: ${hindiCount}, English chars: ${englishCount}`);
  console.log(`Hindi percentage: ${hindiPercentage}%`);
  console.log(`Detected as: ${detectLanguage(text)}`);
  console.log('---');
});

console.log('\n🎯 Test Summary:');
const passedTests = testCases.filter(test => detectLanguage(test.text) === test.expected).length;
console.log(`Passed: ${passedTests}/${testCases.length} tests`);

if (passedTests === testCases.length) {
  console.log('🎉 All tests passed! Hindi detection is working correctly.');
} else {
  console.log('⚠️  Some tests failed. Review the detection logic.');
}

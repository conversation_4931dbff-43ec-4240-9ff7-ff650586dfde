import { NextResponse } from 'next/server';
import { TextToSpeechClient } from '@google-cloud/text-to-speech';

// Initialize the Text-to-Speech client
let ttsClient;

try {
  // Initialize with service account credentials
  ttsClient = new TextToSpeechClient({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS, // Path to service account key file
    // Alternative: use credentials object if you have the key as environment variables
    credentials: process.env.FIREBASE_PRIVATE_KEY ? {
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    } : undefined,
  });
} catch (error) {
  console.error('Failed to initialize Text-to-Speech client:', error);
}

export async function POST(request) {
  try {
    // Check if TTS client is initialized
    if (!ttsClient) {
      return NextResponse.json(
        { error: 'Text-to-Speech service not available' },
        { status: 503 }
      );
    }

    const { text } = await request.json();

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    // Limit text length to prevent abuse
    if (text.length > 5000) {
      return NextResponse.json(
        { error: 'Text is too long. Maximum 5000 characters allowed.' },
        { status: 400 }
      );
    }

    // Configure the TTS request
    const request_config = {
      input: { text: text },
      voice: {
        languageCode: 'en-US',
        name: 'en-US-Journey-D', // A natural-sounding voice
        ssmlGender: 'NEUTRAL',
      },
      audioConfig: {
        audioEncoding: 'MP3',
        speakingRate: 1.0,
        pitch: 0.0,
        volumeGainDb: 0.0,
      },
    };

    console.log('Generating speech for text:', text.substring(0, 100) + '...');

    // Generate the speech
    const [response] = await ttsClient.synthesizeSpeech(request_config);

    if (!response.audioContent) {
      throw new Error('No audio content received from TTS service');
    }

    // Return the audio as a blob
    return new NextResponse(response.audioContent, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': response.audioContent.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Text-to-Speech error:', error);

    // Handle specific Google Cloud errors
    if (error.code === 7) { // PERMISSION_DENIED
      return NextResponse.json(
        { error: 'Authentication failed. Please check your Google Cloud credentials.' },
        { status: 401 }
      );
    }

    if (error.code === 8) { // RESOURCE_EXHAUSTED
      return NextResponse.json(
        { error: 'Text-to-Speech quota exceeded. Please try again later.' },
        { status: 429 }
      );
    }

    if (error.code === 3) { // INVALID_ARGUMENT
      return NextResponse.json(
        { error: 'Invalid text provided for speech synthesis.' },
        { status: 400 }
      );
    }

    // Generic error response
    return NextResponse.json(
      { error: 'Failed to generate speech. Please try again.' },
      { status: 500 }
    );
  }
}

// Optional: Add a GET endpoint for health check
export async function GET() {
  try {
    if (!ttsClient) {
      return NextResponse.json(
        { status: 'unavailable', message: 'Text-to-Speech service not initialized' },
        { status: 503 }
      );
    }

    // Test with a simple phrase
    const testRequest = {
      input: { text: 'Hello' },
      voice: {
        languageCode: 'en-US',
        ssmlGender: 'NEUTRAL',
      },
      audioConfig: {
        audioEncoding: 'MP3',
      },
    };

    await ttsClient.synthesizeSpeech(testRequest);

    return NextResponse.json({
      status: 'available',
      message: 'Text-to-Speech service is working correctly'
    });

  } catch (error) {
    console.error('TTS health check failed:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Text-to-Speech service is not working properly',
        error: error.message 
      },
      { status: 503 }
    );
  }
}

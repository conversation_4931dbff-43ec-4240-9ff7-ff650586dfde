import { NextResponse } from 'next/server';

/**
 * Godot Image Configuration API
 * Provides configuration for <PERSON><PERSON>'s new image loading system
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');

    console.log('🎮 <PERSON>ot requesting image configuration for chat:', chatId);

    // Configuration for <PERSON><PERSON>'s image loading system
    const config = {
      // Firebase Storage configuration
      firebaseStorage: {
        enabled: true,
        basePath: `background/${chatId || '{chatId}'}`,
        supportedFormats: ['.jpg', '.jpeg', '.png', '.svg'],
        timeout: 10000, // 10 seconds
        retryAttempts: 3
      },
      
      // Custom image sources (if needed)
      customSources: [],
      
      // Local fallback configuration
      localFallback: {
        enabled: true,
        basePath: 'moneytales',
        defaultImage: 'default_background.jpg'
      },
      
      // Caching configuration
      cache: {
        enabled: true,
        maxSize: 50, // Maximum number of cached images
        ttl: 3600000 // 1 hour in milliseconds
      },
      
      // Debug configuration
      debug: {
        enabled: true,
        logLevel: 'info'
      }
    };

    // If chatId is provided, include specific image information
    if (chatId) {
      config.chatSpecific = {
        chatId: chatId,
        expectedImagePath: `background/${chatId}/`,
        imageConfigUrl: `/api/chats/${chatId}/gameData`
      };
    }

    console.log('✅ Godot configuration prepared:', {
      chatId: chatId,
      firebaseEnabled: config.firebaseStorage.enabled,
      basePath: config.firebaseStorage.basePath
    });

    return NextResponse.json(config);

  } catch (error) {
    console.error('❌ Error generating Godot configuration:', error);
    return NextResponse.json(
      { error: 'Failed to generate configuration' },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to update Godot configuration (if needed)
 */
export async function POST(request) {
  try {
    const configUpdate = await request.json();
    console.log('🎮 Godot configuration update received:', configUpdate);

    // Here you could save custom configuration if needed
    // For now, just acknowledge the update

    return NextResponse.json({
      success: true,
      message: 'Configuration updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating Godot configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update configuration' },
      { status: 500 }
    );
  }
}

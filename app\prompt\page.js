'use client';
import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';

// Simple audio book story creation component
function AudioBookCreator() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Form data for audio book story creation
  const [formData, setFormData] = useState({
    bookTitle: '',
    author: '',
    changeLocation: '',
    whatIfPrompt: ''
  });

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      if (!currentUser) {
        router.push('/register');
        return;
      }
      setUser(currentUser);
    });

    return () => unsubscribe();
  }, [router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.bookTitle || !formData.author || !formData.changeLocation || !formData.whatIfPrompt) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Generate story tree using the story generation API
      const response = await fetch('/api/generate-story-tree', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to generate story');
      }

      const result = await response.json();

      if (result.success) {
        // Store the story data and redirect to story reader
        localStorage.setItem('generatedStoryData', JSON.stringify(result));
        localStorage.setItem('bookInfo', JSON.stringify(formData));

        // Redirect to story reader with the chat ID
        if (result.chatId) {
          router.push(`/story-reader/${result.chatId}`);
        } else {
          // Fallback: redirect to dashboard
          router.push('/dashboard');
        }
      } else {
        throw new Error(result.error || 'Failed to generate story');
      }
    } catch (error) {
      console.error('Error generating story:', error);
      setError(error.message || 'Failed to generate story. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-app-pattern">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">Create Audio Book Story</h1>
          <p className="text-gray-600">
            Enter book details to generate an interactive audio story with text-to-speech
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Book Title */}
            <div>
              <label htmlFor="bookTitle" className="block text-sm font-medium text-gray-700 mb-2">
                Book Title *
              </label>
              <input
                type="text"
                id="bookTitle"
                name="bookTitle"
                value={formData.bookTitle}
                onChange={handleInputChange}
                placeholder="e.g., The Great Gatsby, रामायण, गीता"
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Author */}
            <div>
              <label htmlFor="author" className="block text-sm font-medium text-gray-700 mb-2">
                Author *
              </label>
              <input
                type="text"
                id="author"
                name="author"
                value={formData.author}
                onChange={handleInputChange}
                placeholder="e.g., F. Scott Fitzgerald, वाल्मीकि, व्यास"
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Change Location */}
            <div>
              <label htmlFor="changeLocation" className="block text-sm font-medium text-gray-700 mb-2">
                Story Setting/Location *
              </label>
              <input
                type="text"
                id="changeLocation"
                name="changeLocation"
                value={formData.changeLocation}
                onChange={handleInputChange}
                placeholder="e.g., West Egg, अयोध्या, कुरुक्षेत्र"
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* What-If Prompt */}
            <div>
              <label htmlFor="whatIfPrompt" className="block text-sm font-medium text-gray-700 mb-2">
                What-If Scenario *
              </label>
              <textarea
                id="whatIfPrompt"
                name="whatIfPrompt"
                value={formData.whatIfPrompt}
                onChange={handleInputChange}
                placeholder="e.g., What if Gatsby never met Daisy?, यदि राम वनवास नहीं गए होते तो क्या होता?"
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex items-center justify-between">
              <Link
                href="/dashboard"
                className="text-gray-600 hover:text-gray-800 transition-colors"
              >
                ← Back to Dashboard
              </Link>

              <button
                type="submit"
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                    Generating Story...
                  </>
                ) : (
                  'Create Audio Story'
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Info Section */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3">How it works:</h3>
          <ul className="text-blue-700 space-y-2">
            <li>• Enter details about a book you'd like to reimagine</li>
            <li>• Provide a "what-if" scenario to explore alternate storylines</li>
            <li>• AI will generate an interactive story with multiple choices</li>
            <li>• Listen to the story with high-quality text-to-speech (supports Hindi & English)</li>
            <li>• Navigate through different story sections and endings</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default function PromptPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-app-pattern">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8B5CF6]"></div>
      </div>
    }>
      <AudioBookCreator />
    </Suspense>
  );
}

// FIXED: Function to extract text from a page with retry logic
const extractTextFromPage = async (pdf, pageNum, retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      const text = textContent.items.map(item => item.str).join(' ');
      
      // Clean up the page to free memory
      page.cleanup();
      
      return text;
    } catch (error) {
      console.error(`Attempt ${attempt} - Error extracting text from page ${pageNum}:`, error);
      
      if (attempt === retries) {
        console.error(`Failed to extract text from page ${pageNum} after ${retries} attempts`);
        return `[Error extracting text from page ${pageNum}]`;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  return '';
};

// FIXED: Function to extract text from a range of pages with better error handling
const extractTextFromPageRange = async (pdf, startPage, endPage) => {
  if (!pdf) {
    console.error('PDF instance not available');
    return '';
  }

  try {
    console.log(`Extracting text from pages ${startPage} to ${endPage}`);
    
    const textPromises = [];
    for (let i = startPage; i <= endPage; i++) {
      textPromises.push(extractTextFromPage(pdf, i));
    }
    
    const pageTexts = await Promise.all(textPromises);
    const combinedText = pageTexts.join('\n\n').trim();
    
    console.log(`Successfully extracted ${combinedText.length} characters from pages ${startPage}-${endPage}`);
    return combinedText;
  } catch (error) {
    console.error(`Error extracting text from page range ${startPage}-${endPage}:`, error);
    return `[Error extracting text from pages ${startPage}-${endPage}]`;
  }
};

// NEW: Helper function to create default chunks with content
const createDefaultChunks = async (pdf, numPages, setPdfChunks, setError) => {
  try {
    console.log('Creating default chunks with content...');
    
    const chunkSize = 20;
    const defaultChunks = [];

    for (let i = 1; i <= numPages; i += chunkSize) {
      const startPage = i;
      const endPage = Math.min(i + chunkSize - 1, numPages);

      console.log(`Creating chunk for pages ${startPage}-${endPage}`);

      // Extract text content from the page range
      const content = await extractTextFromPageRange(pdf, startPage, endPage);

      const chunk = {
        id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
        title: `Pages ${startPage}-${endPage}`,
        path: [`Pages ${startPage}-${endPage}`],
        startPage: startPage,
        endPage: endPage,
        pageCount: endPage - startPage + 1,
        isDefaultChunk: true,
        content: content
      };

      defaultChunks.push(chunk);
      console.log(`Chunk created with ${content.length} characters`);
    }

    setPdfChunks(defaultChunks);
    console.log('Default chunks created successfully:', defaultChunks.length);
  } catch (error) {
    console.error('Error creating default chunks:', error);
    setError(`Error creating default chunks: ${error.message}`);
  }
};

// NEW: Helper function to process outline items with PDF instance
const processOutlineItems = async (items, pdf) => {
  const result = [];

  for (const item of items) {
    const processedItem = { ...item };

    // Extract page number from destination if available
    if (item.dest) {
      try {
        let destRef = item.dest;
        if (typeof destRef === 'string') {
          destRef = await pdf.getDestination(destRef);
        }

        if (Array.isArray(destRef) && destRef.length > 0) {
          const pageRef = destRef[0];
          const pageNum = await pdf.getPageIndex(pageRef) + 1;
          processedItem.pageNumber = pageNum;
        }
      } catch (error) {
        console.error('Error extracting page number:', error);
      }
    }

    // Process nested items recursively
    if (item.items && item.items.length > 0) {
      processedItem.items = await processOutlineItems(item.items, pdf);
    }

    result.push(processedItem);
  }

  return result;
};

// NEW: Helper function to create outline-based chunks with content
const createOutlineBasedChunks = async (processedOutline, pdf, numPages, setPdfChunks, setError) => {
  try {
    console.log('Creating outline-based chunks with content...');
    
    // Find all sections at the deepest level with page numbers
    const deepestSections = [];

    const findDeepestSections = (items, depth = 0, path = []) => {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const currentPath = [...path, item.title];

        if (item.items && item.items.length > 0) {
          findDeepestSections(item.items, depth + 1, currentPath);
        } else if (item.pageNumber) {
          deepestSections.push({
            title: item.title,
            pageNumber: item.pageNumber,
            depth: depth,
            path: currentPath
          });
        }
      }
    };

    findDeepestSections(processedOutline);
    deepestSections.sort((a, b) => a.pageNumber - b.pageNumber);

    if (deepestSections.length === 0) {
      console.log('No outline-based sections found, creating default chunks...');
      await createDefaultChunks(pdf, numPages, setPdfChunks, setError);
      return;
    }

    // Create chunks based on page ranges with content
    const chunks = [];
    for (let i = 0; i < deepestSections.length; i++) {
      const section = deepestSections[i];
      const nextSection = deepestSections[i + 1];

      const startPage = section.pageNumber;
      const endPage = nextSection ? nextSection.pageNumber - 1 : numPages;

      console.log(`Creating outline chunk for "${section.title}" pages ${startPage}-${endPage}`);

      // Extract text content from the page range
      const content = await extractTextFromPageRange(pdf, startPage, endPage);

      const chunk = {
        id: `chunk-${i + 1}`,
        title: section.title,
        path: section.path,
        startPage: startPage,
        endPage: endPage,
        pageCount: endPage - startPage + 1,
        content: content
      };

      chunks.push(chunk);
      console.log(`Outline chunk created with ${content.length} characters`);
    }

    setPdfChunks(chunks);
    console.log('Outline-based chunks created successfully:', chunks.length);
  } catch (error) {
    console.error('Error creating outline-based chunks:', error);
    // Fallback to default chunks
    console.log('Falling back to default chunks...');
    await createDefaultChunks(pdf, numPages, setPdfChunks, setError);
  }
};



// Extract Book Information utility
const extractBookInformation = async (pdf) => {
  try {
    const metadata = await pdf.getMetadata();
    const info = metadata.info || {};

    return {
      title: info.Title || 'Unknown Title',
      author: info.Author || 'Unknown Author',
      subject: info.Subject || '',
      creator: info.Creator || '',
      producer: info.Producer || '',
      creationDate: info.CreationDate || null,
      modificationDate: info.ModDate || null,
      keywords: info.Keywords || '',
      pages: pdf.numPages,
      pdfVersion: pdf.pdfInfo?.PDFFormatVersion || 'Unknown'
    };
  } catch (error) {
    console.error('Error extracting book information:', error);
    return {
      title: 'Unknown Title',
      author: 'Unknown Author',
      subject: '',
      creator: '',
      producer: '',
      creationDate: null,
      modificationDate: null,
      keywords: '',
      pages: pdf.numPages,
      pdfVersion: 'Unknown'
    };
  }
};





// Create a wrapper component that uses the search params
function PromptContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // State variables - simplified for streamlined experience
  const [isClient, setIsClient] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');
  const [userId, setUserId] = useState(null);
  const [chatId, setChatId] = useState(null);
  const [isGeneratingEmbeddings, setIsGeneratingEmbeddings] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGeneratingStory, setIsGeneratingStory] = useState(false);
  const fileInputRef = useRef(null);

  useEffect(() => {
    setIsClient(true);

    // Check for chat ID in URL parameters
    const chatIdParam = searchParams.get('chatId');
    if (!chatIdParam) {
      // Redirect to home or chat list if no chat ID provided
      router.push('/');
      return;
    }
    setChatId(chatIdParam);

    // Add auth listener to get current user
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUserId(user.uid);
      } else {
        // Redirect to login if no user
        router.push('/register');
      }
    });

    return () => unsubscribe();
  }, [searchParams, router]);

  const handlePromptChange = (e) => {
    setPrompt(e.target.value);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        setFile(null);
        setFileName('');
        return;
      }

      if (selectedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        setFile(null);
        setFileName('');
        return;
      }

      // Reset all related states when new file is selected
      setFile(selectedFile);
      setFileName(selectedFile.name);
      setError('');
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];

      if (droppedFile.type !== 'application/pdf') {
        setError('Please upload a PDF file');
        return;
      }

      if (droppedFile.size > 30 * 1024 * 1024) { // 30MB limit
        setError('File size should be less than 30MB');
        return;
      }

      // Reset all related states when new file is dropped
      setFile(droppedFile);
      setFileName(droppedFile.name);
      setError('');
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  // Combined handleSubmit function - processes PDF, generates embeddings, and analyzes in one go
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!file) {
      setError('Please upload a PDF file first');
      return;
    }

    if (!prompt || !prompt.trim()) {
      setError('Please enter a prompt to analyze');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Step 1: Import PDF.js with better error handling
      let pdfJS;
      try {
        pdfJS = await import('pdfjs-dist/build/pdf');
      } catch (importError) {
        console.error('Failed to import PDF.js:', importError);
        throw new Error('Failed to load PDF processing library. Please refresh the page and try again.');
      }

      // Set the worker source with fallback options
      if (typeof window !== 'undefined') {
        try {
          pdfJS.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';
          const testWorker = new Worker('/pdf.worker.min.mjs');
          testWorker.terminate();
        } catch (workerError) {
          console.warn('Primary worker failed, trying fallback:', workerError);
          pdfJS.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfJS.version}/pdf.worker.min.mjs`;
        }
      }

      // Step 2: Load and process PDF
      const arrayBuffer = await file.arrayBuffer();
      const loadingTask = pdfJS.getDocument({
        data: arrayBuffer,
        verbosity: 0
      });

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('PDF loading timeout')), 30000);
      });

      const pdf = await Promise.race([loadingTask.promise, timeoutPromise]);

      // Extract book information for story generation
      const bookInformation = await extractBookInformation(pdf);
      console.log('Book Information:', bookInformation);

      // Get total pages
      const numPages = pdf.numPages;

      // Extract outline and create chunks
      const outline = await pdf.getOutline();
      console.log('Outline:', outline);

      let createdChunks;
      if (!outline || outline.length === 0) {
        console.log('No outline found in the PDF - creating default chunks');
        // We'll create chunks directly below for immediate use
        // Get the chunks from state after they're created
        createdChunks = await new Promise((resolve) => {
          const checkChunks = () => {
            // We need to access the chunks directly since state updates are async
            setTimeout(() => {
              // Create chunks directly here for immediate use
              const createChunksDirectly = async () => {
                const chunkSize = 20;
                const chunks = [];
                for (let i = 1; i <= numPages; i += chunkSize) {
                  const startPage = i;
                  const endPage = Math.min(i + chunkSize - 1, numPages);
                  const content = await extractTextFromPageRange(pdf, startPage, endPage);
                  chunks.push({
                    id: `default-chunk-${Math.floor(i / chunkSize) + 1}`,
                    title: `Pages ${startPage}-${endPage}`,
                    path: [`Pages ${startPage}-${endPage}`],
                    startPage: startPage,
                    endPage: endPage,
                    pageCount: endPage - startPage + 1,
                    isDefaultChunk: true,
                    content: content
                  });
                }
                resolve(chunks);
              };
              createChunksDirectly();
            }, 100);
          };
          checkChunks();
        });
      } else {
        // Process outline and create outline-based chunks
        const processedOutline = await processOutlineItems(outline, pdf);
        // We'll create chunks directly below for immediate use
        // Create chunks directly for immediate use
        createdChunks = await new Promise((resolve) => {
          setTimeout(async () => {
            // Create outline-based chunks directly
            const deepestSections = [];
            const findDeepestSections = (items, depth = 0, path = []) => {
              for (let i = 0; i < items.length; i++) {
                const item = items[i];
                const currentPath = [...path, item.title];
                if (item.items && item.items.length > 0) {
                  findDeepestSections(item.items, depth + 1, currentPath);
                } else if (item.pageNumber) {
                  deepestSections.push({
                    title: item.title,
                    pageNumber: item.pageNumber,
                    depth: depth,
                    path: currentPath
                  });
                }
              }
            };
            findDeepestSections(processedOutline);
            deepestSections.sort((a, b) => a.pageNumber - b.pageNumber);

            const chunks = [];
            for (let i = 0; i < deepestSections.length; i++) {
              const section = deepestSections[i];
              const nextSection = deepestSections[i + 1];
              const startPage = section.pageNumber;
              const endPage = nextSection ? nextSection.pageNumber - 1 : numPages;
              const content = await extractTextFromPageRange(pdf, startPage, endPage);
              chunks.push({
                id: `chunk-${i + 1}`,
                title: section.title,
                path: section.path,
                startPage: startPage,
                endPage: endPage,
                pageCount: endPage - startPage + 1,
                content: content
              });
            }
            resolve(chunks);
          }, 100);
        });
      }

      // Step 3: Generate embeddings automatically
      setIsGeneratingEmbeddings(true);
      console.log('Generating embeddings for chunks...');

      const onProgress = (current, total, chunkId) => {
        // Progress tracking removed for streamlined experience
        console.log(`Processing chunk ${current}/${total}: ${chunkId}`);
      };

      const embedded = await generateEmbeddingsForChunks(createdChunks, onProgress);
      setIsGeneratingEmbeddings(false);
      console.log('Embeddings generated successfully');

      // Step 4: Analyze prompt automatically
      setIsAnalyzing(true);
      console.log('Analyzing prompt against embeddings...');
      const results = await rankChunksByRelevance(prompt, embedded);
      setIsAnalyzing(false);

      // Save analysis results and prompt to localStorage
      try {
        localStorage.setItem('analysisResults', JSON.stringify(results));
        localStorage.setItem('userPrompt', prompt);
        console.log('Analysis results and prompt saved to localStorage');
      } catch (storageError) {
        console.error('Error saving data to localStorage:', storageError);
      }

      // Step 5: Save user prompt, PDF metadata, and analysis results to database
      console.log('💾 Saving data to database...');
      const savePromises = [
        saveUserPromptToDatabase(prompt, fileName),
        savePdfMetadataToDatabase(bookInformation, fileName),
        saveAnalysisToDatabase(results, prompt, bookInformation)
      ];

      // Execute all saves in parallel for better performance
      await Promise.allSettled(savePromises);
      console.log('✅ Database save operations completed');

      // Step 6: Generate story data and wait for completion
      let storyDataGenerated = false;
      if (results && results.length > 0 && bookInformation) {
        console.log('Generating story data...');
        storyDataGenerated = await generateStoryData(results, prompt, bookInformation);
      }

      // Step 7: Wait for all database operations to complete before redirecting
      if (storyDataGenerated) {
        console.log('⏳ Waiting for all database operations to complete...');
        // Add a small delay to ensure all async operations finish
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Step 8: Log summary of what was saved
      console.log('📊 Processing Summary:');
      console.log(`- Chat ID: ${chatId}`);
      console.log(`- User ID: ${userId}`);
      console.log(`- PDF File: ${fileName}`);
      console.log(`- Book Title: ${bookInformation?.title || 'Unknown'}`);
      console.log(`- Author: ${bookInformation?.author || 'Unknown'}`);
      console.log(`- User Prompt: ${prompt.substring(0, 100)}...`);
      console.log(`- Analysis Results: ${results?.length || 0} chunks processed`);
      console.log(`- Firebase Storage Path: background/${chatId}/`);
      console.log(`- Story Data Generated: ${storyDataGenerated ? 'Yes' : 'No'}`);
      console.log('✅ All processing complete - redirecting to game...');

      // Step 9: Automatically redirect to alternate scenario page with chatId
      router.push(`/alternate-scenario?chatId=${chatId}`);

    } catch (error) {
      console.error('Error processing:', error);
      setError(`Error processing: ${error.message}`);
    } finally {
      setIsLoading(false);
      setIsGeneratingEmbeddings(false);
      setIsAnalyzing(false);
    }
  };

  // Function to save user prompt to database
  const saveUserPromptToDatabase = async (userPrompt, fileName) => {
    try {
      if (!chatId || !userId) {
        console.log('Missing chatId or userId, skipping user prompt database save');
        return;
      }

      console.log('Saving user prompt to database...');

      // Save the user prompt as a message
      const promptMessage = {
        content: JSON.stringify({
          prompt: userPrompt,
          fileName: fileName,
          timestamp: new Date().toISOString()
        }),
        type: 'user_prompt'
      };

      const messageResponse = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(promptMessage),
      });

      if (!messageResponse.ok) {
        throw new Error(`Failed to save user prompt: ${messageResponse.status}`);
      }

      console.log('✅ User prompt saved to database successfully');
    } catch (error) {
      console.error('❌ Error saving user prompt to database:', error);
      // Don't throw error as this is not critical for the user experience
    }
  };

  // Function to save PDF metadata to database
  const savePdfMetadataToDatabase = async (bookInformation, fileName) => {
    try {
      if (!chatId || !userId) {
        console.log('Missing chatId or userId, skipping PDF metadata database save');
        return;
      }

      console.log('Saving PDF metadata to database...');

      // Save the PDF metadata as a message
      const metadataMessage = {
        content: JSON.stringify({
          fileName: fileName,
          bookInformation: bookInformation,
          timestamp: new Date().toISOString()
        }),
        type: 'pdf_metadata'
      };

      const messageResponse = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metadataMessage),
      });

      if (!messageResponse.ok) {
        throw new Error(`Failed to save PDF metadata: ${messageResponse.status}`);
      }

      console.log('✅ PDF metadata saved to database successfully');
    } catch (error) {
      console.error('❌ Error saving PDF metadata to database:', error);
      // Don't throw error as this is not critical for the user experience
    }
  };

  // Function to save analysis results to database
  const saveAnalysisToDatabase = async (analysisResults, userPrompt, bookInformation) => {
    try {
      if (!chatId || !userId) {
        console.log('Missing chatId or userId, skipping database save');
        return;
      }

      console.log('Saving analysis data to database...');

      // Save the analysis results as a message
      const analysisMessage = {
        content: JSON.stringify({
          prompt: userPrompt,
          bookInformation: bookInformation,
          analysisResults: analysisResults.slice(0, 5), // Save top 5 results to avoid size limits
          timestamp: new Date().toISOString()
        }),
        type: 'analysis_result'
      };

      const messageResponse = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisMessage),
      });

      if (!messageResponse.ok) {
        throw new Error(`Failed to save analysis message: ${messageResponse.status}`);
      }

      console.log('✅ Analysis results saved to database successfully');
    } catch (error) {
      console.error('❌ Error saving analysis to database:', error);
      // Don't throw error as this is not critical for the user experience
    }
  };

  // Function to generate story data - FIXED: Accept bookInformation parameter to prevent duplicate calls
  const generateStoryData = async (analysisResults, userPrompt, bookInformation) => {
    // Prevent multiple simultaneous calls
    if (isGeneratingStory) {
      console.log('Story generation already in progress, skipping...');
      return false;
    }

    setIsGeneratingStory(true);

    try {
      if (!bookInformation || !bookInformation.title) {
        console.log('No book info available for story generation');
        return false;
      }

      console.log('Generating story data with:', { bookInformation, userPrompt });

      // Generate the game data using the API endpoint
      const response = await fetch('/api/alternate-scenario-game-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bookTitle: bookInformation.title,
          author: bookInformation.author,
          changeLocation: analysisResults[0]?.title || '',
          whatIfPrompt: userPrompt,
          originalEvent: analysisResults[0]?.content?.substring(0, 200) + '...' || '',
          chatId: chatId // Pass chatId for proper Firebase Storage organization
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const storyData = await response.json();

      if (storyData && storyData.questions) {
        // Validate story data format
        if (validateStoryData(storyData)) {
          // Store the game data in localStorage for the Godot game to access
          localStorage.setItem('storyGameData', JSON.stringify(storyData));

          // Also make it globally accessible for Godot
          window.storyGameData = storyData;

          // Save story data to database and wait for completion
          console.log('💾 Saving story data to database...');
          await saveStoryDataToDatabase(storyData);

          // Save image metadata to database if image exists and wait for completion
          if (storyData.backgroundImageUrl) {
            console.log('🖼️ Saving image metadata to database...');
            await saveImageMetadataToDatabase(storyData);
          }

          console.log('Story data generated and stored successfully:', storyData);
          console.log('✅ Story data generated and ready for game!');
          return true; // Return success
        } else {
          console.error('Generated story data failed validation');
          return false;
        }
      } else {
        console.error('Invalid story data received:', storyData);
        return false;
      }
    } catch (error) {
      console.error('Error generating story data:', error);
      // Don't show this error to user as it's not critical for the analysis
      return false;
    } finally {
      setIsGeneratingStory(false);
    }
  };

  // Function to save story data to database
  const saveStoryDataToDatabase = async (storyData) => {
    try {
      if (!chatId) {
        console.log('Missing chatId, skipping story data database save');
        return;
      }

      console.log('Saving story data to database...');

      // Story data now contains Firebase Storage URLs instead of base64, so no size issues
      const storyDataResponse = await fetch(`/api/chats/${chatId}/storyData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (!storyDataResponse.ok) {
        throw new Error(`Failed to save story data: ${storyDataResponse.status}`);
      }

      console.log('✅ Story data saved to database successfully');
      console.log('🔗 Background image URL included:', !!storyData.backgroundImageUrl);
    } catch (error) {
      console.error('❌ Error saving story data to database:', error);
      // Don't throw error as this is not critical for the user experience
    }
  };

  // Base64 image processing removed - Godot now handles Firebase Storage URLs directly

  // Function to save image metadata to database
  const saveImageMetadataToDatabase = async (storyData) => {
    try {
      if (!chatId) {
        console.log('Missing chatId, skipping image metadata database save');
        return;
      }

      console.log('Saving image metadata to database...');

      // Extract image information from story data
      const imageInfo = {
        hasBackgroundImage: !!storyData.backgroundImageUrl,
        imageSource: 'freepik',
        generatedAt: new Date().toISOString(),
        storyTitle: storyData.story_tree?.root?.title || 'Unknown',
        imagePrompt: storyData.story_tree?.root?.imagePrompt || '',
        firebaseUrl: storyData.backgroundImageUrl || storyData.story_tree?.root?.image?.firebaseUrl || ''
      };

      const imageResponse = await fetch(`/api/chats/${chatId}/images`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: imageInfo.firebaseUrl,
          prompt: imageInfo.imagePrompt,
          nodeId: 'root',
          metadata: imageInfo
        }),
      });

      if (!imageResponse.ok) {
        throw new Error(`Failed to save image metadata: ${imageResponse.status}`);
      }

      console.log('✅ Image metadata saved to database successfully');
      console.log('🔗 Firebase Storage URL:', imageInfo.firebaseUrl);
    } catch (error) {
      console.error('❌ Error saving image metadata to database:', error);
      // Don't throw error as this is not critical for the user experience
    }
  };

  // Function to validate story data format for Godot
  const validateStoryData = (data) => {
    if (!data) {
      console.error('Story data is null or undefined');
      return false;
    }

    if (!data.questions || !Array.isArray(data.questions)) {
      console.error('Story data missing questions array:', data);
      return false;
    }

    if (data.questions.length === 0) {
      console.error('Story data has empty questions array');
      return false;
    }

    // Check if first question has required fields
    const firstQuestion = data.questions[0];
    if (!firstQuestion.question || !firstQuestion.options) {
      console.error('First question missing required fields:', firstQuestion);
      return false;
    }

    console.log('Story data validation passed:', {
      questionsCount: data.questions.length,
      firstQuestion: {
        title: firstQuestion.title,
        hasQuestion: !!firstQuestion.question,
        optionsCount: firstQuestion.options?.length || 0,
        level: firstQuestion.level
      }
    });

    return true;
  };

  if (!isClient) {
    return (
      <div className="min-h-screen page-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-green"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen page-background">
      {/* Header */}
      <header className="bg-primary-green text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="font-slackey text-2xl">MoneyTales</h1>
          <nav className="flex items-center space-x-4">
            <Link
              href="/game"
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-md transition-colors font-medium"
            >
              Play Game
            </Link>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="font-libre text-3xl font-bold mb-6 text-text-primary">Create Alternate Scenario Game</h1>
          <p className="text-gray-600 mb-8">Upload a PDF and enter a "what if" prompt to generate an interactive alternate scenario game.</p>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <form onSubmit={handleSubmit}>
              {/* What If Prompt Input */}
              <div className="mb-6">
                <label htmlFor="prompt" className="block text-gray-700 font-medium mb-2">
                  What if scenario prompt
                </label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={handlePromptChange}
                  placeholder="What if a different decision was made? What if an event happened differently? Describe your alternate scenario..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-green focus:border-transparent min-h-[120px]"
                />
              </div>

              {/* File Upload */}
              <div className="mb-6">
                <label className="block text-gray-700 font-medium mb-2">
                  Upload PDF
                </label>
                <div
                  className={`border-2 border-dashed rounded-md p-8 text-center cursor-pointer transition-colors ${
                    error && !file ? 'border-error' : 'border-gray-300 hover:border-primary-green'
                  }`}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={triggerFileInput}
                >
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="application/pdf"
                    className="hidden"
                  />

                  {!file ? (
                    <div>
                      <div className="flex justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      </div>
                      <p className="text-gray-600 mb-1">Drag and drop your PDF here, or click to browse</p>
                      <p className="text-gray-500 text-sm">Maximum file size: 30MB</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div className="text-left">
                        <p className="text-primary-green font-medium">{fileName}</p>
                        <button
                          type="button"
                          className="text-sm text-gray-500 hover:text-error"
                          onClick={(e) => {
                            e.stopPropagation();
                            setFile(null);
                            setFileName('');
                            setError('');
                          }}
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4 p-3 bg-error/10 text-error rounded-md">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full px-4 py-3 rounded-md font-medium transition-all duration-300 ${
                  isLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-primary-green text-white hover:bg-primary-green-dark'
                }`}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating your game...
                  </span>
                ) : (
                  'Create Alternate Scenario Game'
                )}
              </button>

              {/* Processing Status */}
              {isLoading && (
                <div className="mt-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                      <div className="bg-primary-green h-2.5 rounded-full animate-pulse" style={{
                        width: isGeneratingEmbeddings
                          ? '60%'
                          : isAnalyzing
                            ? '80%'
                            : isGeneratingStory
                              ? '95%'
                              : '40%'
                      }}></div>
                    </div>
                    <span className="whitespace-nowrap">
                      {isGeneratingEmbeddings
                        ? 'Analyzing content...'
                        : isAnalyzing
                          ? 'Processing scenario...'
                          : isGeneratingStory
                            ? 'Creating game...'
                            : 'Reading PDF...'}
                    </span>
                  </div>
                </div>
              )}
            </form>
          </div>


        </div>
      </main>
    </div>
  );
}

// Main component that wraps the content in a Suspense boundary
export default function Home() {
  return (
    <Suspense fallback={
      <div className="min-h-screen page-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-green"></div>
      </div>
    }>
      <PromptContent />
    </Suspense>
  );
}
# Story Reader Feature

## Overview

The Story Reader feature allows users to read and listen to their interactive stories with text-to-speech functionality. This feature provides an accessible way to consume story content through both visual and auditory means.

## Features

### 🎧 Text-to-Speech Integration
- **Google Cloud Text-to-Speech**: High-quality speech synthesis using Google's advanced TTS API
- **Multiple Audio Controls**: Play, pause, stop, and volume controls for each story section
- **Progress Tracking**: Visual progress bar showing current playback position
- **Individual Section Audio**: Each story section, title, and choice can be read aloud separately

### 📖 Story Content Display
- **Structured Layout**: Clean, readable presentation of story content
- **Section Navigation**: Easy navigation between different story sections
- **Choice Display**: Interactive display of story choices with individual audio controls
- **Story Metadata**: Display of story information including title, author, and scenario details

### 📱 Responsive Design
- **Mobile-First**: Optimized for mobile devices with touch-friendly controls
- **Adaptive Layout**: Responsive design that works across all screen sizes
- **Accessible Controls**: Large, clearly labeled buttons with proper contrast

### 🎮 Integration with Existing Features
- **Chat Integration**: Accessible from chat pages where story data exists
- **Seamless Navigation**: Easy access alongside the existing game functionality
- **Consistent Styling**: Uses the existing theme system for consistent UI

## Technical Implementation

### Components

#### 1. Story Reader Page (`/app/story-reader/[chatId]/page.js`)
- Main page component that fetches and displays story data
- Handles authentication and error states
- Manages navigation between story sections

#### 2. AudioPlayer Component (`/app/components/AudioPlayer.js`)
- Reusable audio player with full playback controls
- Integrates with the TTS API for audio generation
- Provides progress tracking and volume control

#### 3. StoryContent Component (`/app/components/StoryContent.js`)
- Displays formatted story content with proper typography
- Handles section navigation and choice display
- Integrates audio players for each content section

#### 4. Text-to-Speech API (`/app/api/text-to-speech/route.js`)
- Server-side API that interfaces with Google Cloud TTS
- Handles authentication and error management
- Returns audio data as MP3 format

### Data Flow

1. **Story Data Retrieval**: Fetches story data from Firestore using existing API endpoints
2. **Content Display**: Renders story content with proper formatting and navigation
3. **Audio Generation**: Converts text to speech using Google Cloud TTS API
4. **Audio Playback**: Manages audio playback with full user controls

## Usage

### Accessing the Story Reader

1. Navigate to a chat that contains story data
2. Click the "Read Story" button in the chat interface
3. The story reader will open with the first section displayed

### Reading and Listening

1. **Visual Reading**: Story content is displayed with clear typography and formatting
2. **Audio Playback**: Click any "Play" button to hear the content read aloud
3. **Navigation**: Use the navigation controls to move between story sections
4. **Volume Control**: Adjust volume or mute audio using the volume controls

### Story Sections

- **Title**: Each section's title can be read separately
- **Main Content**: The primary story text with full audio controls
- **Choices**: Individual story choices with their own audio playback
- **Metadata**: Story information and context

## Configuration

### Environment Variables

The following environment variables are required for the TTS functionality:

```env
# Google Cloud Configuration (already configured for Firebase)
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=your-service-account-email
FIREBASE_PRIVATE_KEY=your-service-account-private-key
```

### Google Cloud Text-to-Speech Setup

1. The feature uses the existing Firebase service account credentials
2. Ensure the Text-to-Speech API is enabled in your Google Cloud project
3. The service account should have the necessary permissions for TTS

## Error Handling

### Client-Side
- **Loading States**: Clear loading indicators during data fetching and audio generation
- **Error Messages**: User-friendly error messages for various failure scenarios
- **Fallback Behavior**: Graceful degradation when audio features are unavailable

### Server-Side
- **API Rate Limiting**: Handles Google Cloud API rate limits and quotas
- **Authentication Errors**: Proper error responses for authentication failures
- **Input Validation**: Validates text input and prevents abuse

## Accessibility

### Features
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility for all controls
- **High Contrast**: Sufficient color contrast for readability
- **Large Touch Targets**: Mobile-friendly button sizes

### Audio Controls
- **Clear Labels**: All audio controls have descriptive labels
- **Visual Feedback**: Clear indication of audio playback state
- **Volume Control**: Accessible volume adjustment

## Performance Considerations

### Audio Caching
- **Browser Caching**: Audio files are cached by the browser
- **Efficient Loading**: Audio is generated on-demand to reduce server load
- **Memory Management**: Proper cleanup of audio resources

### Text Processing
- **Length Limits**: Text is limited to prevent abuse and ensure performance
- **Chunking**: Long content is handled efficiently
- **Error Recovery**: Robust error handling for TTS failures

## Future Enhancements

### Potential Features
- **Voice Selection**: Multiple voice options for different preferences
- **Speed Control**: Adjustable playback speed
- **Bookmarking**: Save reading position across sessions
- **Offline Support**: Download audio for offline listening
- **Highlighting**: Visual highlighting of currently spoken text

### Technical Improvements
- **Audio Streaming**: Stream audio for longer content
- **Background Processing**: Generate audio in the background
- **Advanced Caching**: More sophisticated caching strategies
- **Analytics**: Track usage patterns and preferences

## Troubleshooting

### Common Issues

1. **Audio Not Playing**
   - Check browser audio permissions
   - Verify Google Cloud TTS API is enabled
   - Check service account credentials

2. **Slow Audio Generation**
   - Monitor Google Cloud API quotas
   - Check network connectivity
   - Verify server performance

3. **Navigation Issues**
   - Ensure story data is properly formatted
   - Check for JavaScript errors in browser console
   - Verify authentication state

### Support
For technical issues or questions about the Story Reader feature, check the browser console for error messages and verify that all required environment variables are properly configured.

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Play, MessageSquare, Image as ImageIcon, FileText, Calendar, User, Book, Loader2, Volume2 } from 'lucide-react';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged } from 'firebase/auth';

export default function ChatDetailPage() {
  const params = useParams();
  const router = useRouter();
  const chatId = params.chatId;

  const [chat, setChat] = useState(null);
  const [messages, setMessages] = useState([]);
  const [images, setImages] = useState([]);
  const [storyData, setStoryData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentUser, setCurrentUser] = useState(null);

  // Add auth check to ensure users can only access their own chats
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        // Redirect to login if no user
        router.push('/register');
        return;
      }
      
      // Set current user
      setCurrentUser(user);
      
      // Load chat data with auth check
      loadChatData(user.uid);
    });
    
    return () => unsubscribe();
  }, []);

  const loadChatData = async (uid) => {
    try {
      setLoading(true);
      setError('');

      // Check if user has access to this chat
      const userResponse = await fetch(`/api/users/${uid}`);
      if (!userResponse.ok) {
        throw new Error('Failed to load user data');
      }
      
      const userData = await userResponse.json();
      
      // Security check - only allow access to own chats
      if (!userData.chats || !userData.chats.includes(chatId)) {
        router.push('/dashboard');
        throw new Error('Unauthorized access to chat');
      }

      // Load chat details
      const chatResponse = await fetch(`/api/chats/${chatId}`);
      if (!chatResponse.ok) {
        throw new Error('Failed to load chat details');
      }
      const chatData = await chatResponse.json();
      
      setChat(chatData);

      // Load messages
      const messagesResponse = await fetch(`/api/chats/${chatId}/messages`);
      if (messagesResponse.ok) {
        const messagesData = await messagesResponse.json();
        setMessages(messagesData.messages || []);
      }

      // Load images
      const imagesResponse = await fetch(`/api/chats/${chatId}/images`);
      if (imagesResponse.ok) {
        const imagesData = await imagesResponse.json();
        setImages(imagesData.images || []);
      }

      // Load story data (only if it exists)
      const storyResponse = await fetch(`/api/chats/${chatId}/storyData`);
      if (storyResponse.ok) {
        const storyDataResponse = await storyResponse.json();
        setStoryData(storyDataResponse);
      } else if (storyResponse.status === 404) {
        // Story data doesn't exist yet - this is normal for new chats
        console.log('No story data found for this chat yet');
        setStoryData(null);
      } else {
        // Only log actual errors (not 404s)
        console.warn('Failed to fetch story data:', storyResponse.status, storyResponse.statusText);
      }

    } catch (error) {
      console.error('Error loading chat data:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown date';
    
    let date;
    if (timestamp.toDate) {
      date = timestamp.toDate();
    } else {
      date = new Date(timestamp);
    }
    
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePlayGame = () => {
    // Navigate to the game page with the chat ID
    router.push(`/alternate-scenario?chatId=${chatId}`);
  };

  const handleReadStory = () => {
    // Navigate to the story reader page with the chat ID
    router.push(`/story-reader/${chatId}`);
  };

  const getUserQuery = () => {
    // Check for both old and new message types for backward compatibility
    const userMessage = messages.find(msg => msg.type === 'user_query' || msg.type === 'user_prompt');
    if (userMessage) {
      // Handle both direct content and JSON content
      if (userMessage.type === 'user_prompt') {
        try {
          const parsed = JSON.parse(userMessage.content);
          return parsed.prompt || '';
        } catch (error) {
          return userMessage.content || '';
        }
      }
      return userMessage.content || '';
    }
    return '';
  };

  const getAnalysisResults = () => {
    // Check for both old and new message types for backward compatibility
    const analysisMessage = messages.find(msg => msg.type === 'analysis_results' || msg.type === 'analysis_result');
    if (analysisMessage) {
      try {
        const parsed = JSON.parse(analysisMessage.content);
        // Handle the new format where analysis results are nested
        if (parsed.analysisResults) {
          return parsed.analysisResults;
        }
        // Handle old format where results are direct
        return parsed;
      } catch (error) {
        console.error('Error parsing analysis results:', error);
        return null;
      }
    }
    return null;
  };

  const getPdfMetadata = () => {
    const metadataMessage = messages.find(msg => msg.type === 'pdf_metadata');
    if (metadataMessage) {
      try {
        const parsed = JSON.parse(metadataMessage.content);
        return parsed.bookInformation || null;
      } catch (error) {
        console.error('Error parsing PDF metadata:', error);
        return null;
      }
    }
    return null;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#2a2d32] flex items-center justify-center">
        <div className="text-center text-white">
          <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading chat details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#2a2d32] flex items-center justify-center">
        <div className="text-center text-white max-w-md">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
            <h2 className="text-xl font-bold mb-2">Error Loading Chat</h2>
            <p className="text-red-200 mb-4">{error}</p>
            <div className="space-y-2">
              <Link
                href="/dashboard"
                className="block px-6 py-3 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Return to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!chat) {
    return (
      <div className="min-h-screen bg-[#2a2d32] flex items-center justify-center">
        <div className="text-center text-white">
          <p className="text-lg">Chat not found</p>
          <Link
            href="/dashboard"
            className="mt-4 inline-block px-6 py-3 bg-[#8B5CF6] text-white rounded-md hover:bg-[#7C3AED] transition-colors"
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  const analysisResults = getAnalysisResults();
  const userQuery = getUserQuery();
  const pdfMetadata = getPdfMetadata();

  // Debug logging for troubleshooting
  console.log('🔍 Chat Page Debug Info:');
  console.log('- Messages found:', messages.length);
  console.log('- Message types:', messages.map(m => m.type));
  console.log('- User query found:', !!userQuery);
  console.log('- Analysis results found:', !!analysisResults);
  console.log('- PDF metadata found:', !!pdfMetadata);
  console.log('- Images found:', images.length);

  return (
    <div className="min-h-screen bg-[#2a2d32]">
      {/* Header */}
      <header className="bg-[#1e2023] border-b border-[#3a3d42] px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard"
              className="flex items-center text-white hover:text-gray-300 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Dashboard
            </Link>
            <div className="h-6 w-px bg-[#3a3d42]"></div>
            <h1 className="text-xl font-bold text-white">{chat.title}</h1>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-4">
            {storyData && (
              <>
                <button
                  onClick={handleReadStory}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium"
                >
                  <Volume2 className="w-4 h-4" />
                  <span>Read Story</span>
                </button>
                <button
                  onClick={handlePlayGame}
                  className="flex items-center space-x-2 px-4 py-2 bg-[#8B5CF6] text-white rounded-md hover:bg-[#7C3AED] transition-colors font-medium"
                >
                  <Play className="w-4 h-4" />
                  <span>Play Game</span>
                </button>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Chat Info & Messages */}
          <div className="lg:col-span-2 space-y-6">
            {/* Chat Information */}
            <div className="bg-[#1e2023] rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Chat Information
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-3">
                  <div className="flex items-center text-[#696F79]">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>Created: {formatDate(chat.createdAt)}</span>
                  </div>
                  
                  {chat.bookInfo?.bookTitle && (
                    <div className="flex items-center text-[#696F79]">
                      <Book className="w-4 h-4 mr-2" />
                      <span>Book: {chat.bookInfo.bookTitle}</span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-3">
                  {chat.updatedAt && (
                    <div className="flex items-center text-[#696F79]">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>Updated: {formatDate(chat.updatedAt)}</span>
                    </div>
                  )}
                  
                  {chat.bookInfo?.author && (
                    <div className="flex items-center text-[#696F79]">
                      <User className="w-4 h-4 mr-2" />
                      <span>Author: {chat.bookInfo.author}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* User Query */}
            {userQuery && (
              <div className="bg-[#1e2023] rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2" />
                  Your "What If" Question
                </h3>
                <div className="bg-[#2a2d32] rounded-lg p-4">
                  <p className="text-[#e5e7eb]">{userQuery}</p>
                </div>
              </div>
            )}

            {/* PDF Metadata */}
            {pdfMetadata && (
              <div className="bg-[#1e2023] rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                  <Book className="w-5 h-5 mr-2" />
                  Book Information
                </h3>
                <div className="bg-[#2a2d32] rounded-lg p-4 space-y-3">
                  {pdfMetadata.title && (
                    <div className="flex items-start">
                      <span className="text-[#696F79] text-sm w-20 flex-shrink-0">Title:</span>
                      <span className="text-[#e5e7eb] text-sm">{pdfMetadata.title}</span>
                    </div>
                  )}
                  {pdfMetadata.author && (
                    <div className="flex items-start">
                      <span className="text-[#696F79] text-sm w-20 flex-shrink-0">Author:</span>
                      <span className="text-[#e5e7eb] text-sm">{pdfMetadata.author}</span>
                    </div>
                  )}
                  {pdfMetadata.pages && (
                    <div className="flex items-start">
                      <span className="text-[#696F79] text-sm w-20 flex-shrink-0">Pages:</span>
                      <span className="text-[#e5e7eb] text-sm">{pdfMetadata.pages}</span>
                    </div>
                  )}
                  {pdfMetadata.subject && (
                    <div className="flex items-start">
                      <span className="text-[#696F79] text-sm w-20 flex-shrink-0">Subject:</span>
                      <span className="text-[#e5e7eb] text-sm">{pdfMetadata.subject}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Analysis Results */}
            {analysisResults && (
              <div className="bg-[#1e2023] rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Analysis Results
                </h3>
                <div className="space-y-3">
                  <p className="text-[#696F79] text-sm">
                    Found {analysisResults.length} relevant sections from your story
                  </p>
                  
                  <div className="grid gap-3 max-h-96 overflow-y-auto">
                    {analysisResults.slice(0, 5).map((result, index) => (
                      <div key={index} className="bg-[#2a2d32] rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-white text-sm">
                            {result.title || `Section ${index + 1}`}
                          </h4>
                          <span className="text-xs text-[#8B5CF6] bg-[#8B5CF6]/10 px-2 py-1 rounded">
                            {Math.round((result.similarity || result.score || 0) * 100)}% match
                          </span>
                        </div>
                        <p className="text-[#696F79] text-sm">
                          {result.content?.substring(0, 200)}...
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Right Column - Story Data & Images */}
          <div className="space-y-6">
            {/* Story Game Status */}
            <div className="bg-[#1e2023] rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Play className="w-5 h-5 mr-2" />
                Interactive Story
              </h3>
              
              {storyData ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-[#696F79] text-sm">Status</span>
                    <span className="text-green-400 text-sm font-medium">Ready to Play</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-[#696F79] text-sm">Scenarios</span>
                    <span className="text-white text-sm">{storyData.questions?.length || 0}</span>
                  </div>
                  
                  <div className="space-y-2">
                    <button
                      onClick={handleReadStory}
                      className="w-full py-3 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors font-medium flex items-center justify-center space-x-2"
                    >
                      <Volume2 className="w-4 h-4" />
                      <span>Read Story</span>
                    </button>
                    <button
                      onClick={handlePlayGame}
                      className="w-full py-3 px-4 bg-[#8B5CF6] text-white rounded-md hover:bg-[#7C3AED] transition-colors font-medium flex items-center justify-center space-x-2"
                    >
                      <Play className="w-4 h-4" />
                      <span>Launch Game</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-[#696F79] text-sm mb-4">
                    {messages.length === 0
                      ? "This is a new chat session. Upload a PDF and ask a 'what if' question to start your story analysis."
                      : "No interactive story generated yet. Complete the analysis to create your game."
                    }
                  </p>
                  <Link
                    href={`/prompt?chatId=${chatId}`}
                    className="inline-flex items-center px-4 py-2 bg-[#8B5CF6] text-white rounded-md hover:bg-[#7C3AED] transition-colors text-sm"
                  >
                    {messages.length === 0 ? 'Start Analysis' : 'Continue Analysis'}
                  </Link>
                </div>
              )}
            </div>

            {/* Images */}
            {images.length > 0 && (
              <div className="bg-[#1e2023] rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <ImageIcon className="w-5 h-5 mr-2" />
                  Generated Images ({images.length})
                </h3>
                
                <div className="grid grid-cols-2 gap-3">
                  {images.slice(0, 4).map((image, index) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={image.url}
                        alt={image.prompt || `Generated image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <button className="text-white text-xs px-2 py-1 bg-black/50 rounded">
                          View
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                
                {images.length > 4 && (
                  <p className="text-[#696F79] text-sm mt-3 text-center">
                    +{images.length - 4} more images
                  </p>
                )}
              </div>
            )}

            {/* Quick Stats */}
            <div className="bg-[#1e2023] rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Quick Stats</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-[#696F79] text-sm">Messages</span>
                  <span className="text-white text-sm">{messages.length}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-[#696F79] text-sm">Images</span>
                  <span className="text-white text-sm">{images.length}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-[#696F79] text-sm">Analysis Results</span>
                  <span className="text-white text-sm">{analysisResults?.length || 0}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}



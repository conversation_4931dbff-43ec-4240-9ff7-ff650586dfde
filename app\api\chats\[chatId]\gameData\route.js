import { NextResponse } from 'next/server';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp as initializeAdminApp, cert, getApps as getAdminApps } from 'firebase-admin/app';

// Initialize Firebase Admin if not already initialized
if (!getAdminApps().length) {
  initializeAdminApp({
    credential: cert({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    }),
  });
}

const db = getFirestore();

// Base64 conversion removed - Godot now handles Firebase Storage URLs directly

// GET complete game data with Firebase Storage URLs for Godot
export async function GET(request, { params }) {
  try {
    const { chatId } = await params;

    if (!chatId) {
      return NextResponse.json(
        { error: 'Missing chat ID' },
        { status: 400 }
      );
    }

    console.log(`🎮 Fetching complete game data for chat: ${chatId}`);

    // Get story data from database
    const storyDataDoc = await db.collection('chats').doc(chatId).collection('storyData').doc('main').get();

    if (!storyDataDoc.exists) {
      return NextResponse.json(
        { error: 'Story data not found' },
        { status: 404 }
      );
    }

    const storyData = storyDataDoc.data();
    console.log('📄 Story data retrieved from database');

    // Get image data from database
    const imagesSnapshot = await db.collection('chats').doc(chatId).collection('images').get();

    let backgroundImageUrl = null;
    if (!imagesSnapshot.empty) {
      // Get the most recent image (assuming it's the background image)
      const imageDoc = imagesSnapshot.docs[0];
      const imageData = imageDoc.data();

      if (imageData.url) {
        backgroundImageUrl = imageData.url;
        console.log('🔗 Firebase Storage URL found:', backgroundImageUrl);
      }
    }

    // Combine story data with Firebase Storage URL
    const completeGameData = {
      ...storyData,
      backgroundImageUrl: backgroundImageUrl,
      chatId: chatId // Include chatId for Godot's image caching system
    };

    console.log(`✅ Complete game data prepared for chat: ${chatId}`);
    console.log(`- Has background image URL: ${!!backgroundImageUrl}`);
    console.log(`- Background image URL: ${backgroundImageUrl || 'none'}`);
    console.log(`- Chat ID: ${chatId}`);
    console.log(`- Questions count: ${completeGameData.questions?.length || 0}`);
    console.log(`- Game data keys: ${Object.keys(completeGameData)}`);

    return NextResponse.json(completeGameData);

  } catch (error) {
    console.error('❌ Error fetching complete game data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch game data' },
      { status: 500 }
    );
  }
}

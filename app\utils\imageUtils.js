'use client';

/**
 * Utility functions for handling images in the game
 */

/**
 * Fetch an image from a URL and convert it to base64
 * @param {string} imageUrl - The URL of the image to fetch
 * @returns {Promise<string>} Base64 encoded image data
 */
export async function fetchImageAsBase64(imageUrl) {
  try {
    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    // Convert to blob
    const blob = await response.blob();
    
    // Convert blob to base64
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        // Remove the data URL prefix to get just the base64 content
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error fetching image as base64:', error);
    throw error;
  }
}

/**
 * Preload images from Firebase URLs and add base64 data to game data
 * @param {Object} gameData - The game data object
 * @returns {Promise<Object>} Game data with preloaded base64 images
 */
export async function preloadGameImages(gameData) {
  if (!gameData || !gameData.questions) {
    return gameData;
  }

  console.log('Preloading images for Godot game...');
  
  // Create a copy of the game data to avoid mutating the original
  const updatedGameData = JSON.parse(JSON.stringify(gameData));
  
  // Process each question that might have images
  for (const question of updatedGameData.questions) {
    if (question.imageUrl && !question.imageBase64) {
      try {
        console.log(`Fetching image for question ${question.node_id}:`, question.imageUrl);

        // First validate that the URL is accessible
        const isValid = await validateImageUrl(question.imageUrl);
        if (!isValid) {
          console.warn(`Image URL not accessible for question ${question.node_id}:`, question.imageUrl);
          question.imageSource = 'firebase'; // Keep URL but mark as potentially problematic
          continue;
        }

        const base64Data = await fetchImageAsBase64(question.imageUrl);
        question.imageBase64 = base64Data;
        question.imageSource = 'firebase'; // Mark as Firebase source with base64 fallback
        console.log(`Successfully loaded base64 data for question ${question.node_id} (${base64Data.length} chars)`);
      } catch (error) {
        console.error(`Failed to load image for question ${question.node_id}:`, error);
        // Keep the Firebase URL as fallback
        question.imageSource = 'firebase';
      }
    }
  }

  console.log('Image preloading complete');
  return updatedGameData;
}

/**
 * Create a data URL from base64 image data
 * @param {string} base64Data - Base64 encoded image data
 * @param {string} mimeType - MIME type of the image (default: image/jpeg)
 * @returns {string} Data URL
 */
export function createDataUrl(base64Data, mimeType = 'image/jpeg') {
  return `data:${mimeType};base64,${base64Data}`;
}

/**
 * Validate if an image URL is accessible
 * @param {string} imageUrl - The image URL to validate
 * @returns {Promise<boolean>} True if the image is accessible
 */
export async function validateImageUrl(imageUrl) {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error validating image URL:', error);
    return false;
  }
}

/**
 * Get the best available image source from image data
 * @param {Object} imageData - Image data object
 * @returns {string|null} The best available image source
 */
export function getBestImageSource(imageData) {
  if (!imageData) return null;
  
  // Prioritize Firebase URL if available
  if (imageData.imageUrl || imageData.firebaseUrl) {
    return imageData.imageUrl || imageData.firebaseUrl;
  }
  
  // Fallback to base64 data URL
  if (imageData.imageBase64 || imageData.base64) {
    const base64Data = imageData.imageBase64 || imageData.base64;
    return createDataUrl(base64Data);
  }
  
  return null;
}

/**
 * Prepare image data for Godot game consumption
 * @param {Object} imageData - Raw image data
 * @returns {Object} Formatted image data for Godot
 */
export function formatImageForGodot(imageData) {
  if (!imageData) return null;
  
  return {
    // Firebase URL for web display
    imageUrl: imageData.firebaseUrl || imageData.imageUrl,
    // Base64 data for Godot engine
    imageBase64: imageData.base64 || imageData.imageBase64,
    // Source type
    imageSource: imageData.firebaseUrl ? 'firebase' : 'base64',
    // Additional metadata
    imageId: imageData.imageId,
    metadata: imageData.metadata,
    prompt: imageData.prompt || imageData.metadata?.prompt
  };
}

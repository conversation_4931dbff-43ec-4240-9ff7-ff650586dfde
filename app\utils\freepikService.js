'use client';

import { ref, uploadString, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate an image using Freepik API based on scene visuals
 * @param {Object} sceneVisuals - The scene visuals from the alternate scenario
 * @param {string} sceneVisuals.background_description - Background description
 * @param {string} sceneVisuals.character_state - Character state description
 * @param {string} sceneVisuals.scene_mood - Scene mood description
 * @param {string} chatId - The chat ID for organizing storage
 * @returns {Promise<Object>} The generated image data
 */
export async function generateSceneImage(sceneVisuals, chatId = null) {
  try {
    // Construct a comprehensive prompt from the scene visuals
    const prompt = constructImagePrompt(sceneVisuals);

    // Call the API route instead of directly using the Freepik API
    const response = await fetch('/api/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        sceneVisuals,
        chatId
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: result.error || `Server error: ${response.status}`
      };
    }

    // Store the image in Firebase Storage if generation was successful
    if (result.success && result.data?.base64) {
      try {
        const imageUrl = await storeImageInFirebase(result.data.base64, prompt, chatId);
        // Add the Firebase URL to the result
        result.data.firebaseUrl = imageUrl;
      } catch (storageError) {
        console.error('Error storing image in Firebase:', storageError);
        // Continue with the result even if storage fails
      }
    }

    return result;
  } catch (error) {
    console.error('Error generating scene image:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

/**
 * Store a base64 image in Firebase Storage
 * @param {string} base64Data - The base64 image data
 * @param {string} prompt - The prompt used to generate the image
 * @param {string} chatId - The chat ID for organizing storage
 * @returns {Promise<string>} The download URL of the stored image
 */
async function storeImageInFirebase(base64Data, prompt, chatId = null) {
  // Generate a unique ID for the image
  const imageId = uuidv4();

  // Create a reference to the background folder with chatId structure
  const storagePath = chatId ? `background/${chatId}/${imageId}.jpg` : `background/${imageId}.jpg`;
  const storageRef = ref(storage, storagePath);
  
  // Remove the data URL prefix if present
  const base64Content = base64Data.includes(',') 
    ? base64Data.split(',')[1] 
    : base64Data;
  
  // Upload the image as a base64 string
  await uploadString(storageRef, base64Content, 'base64');
  
  // Get the download URL
  const downloadUrl = await getDownloadURL(storageRef);
  
  // Save metadata about the image to Firestore if needed
  // This part is optional but recommended for tracking
  // You could add code here to save prompt, timestamp, etc.
  
  return downloadUrl;
}

/**
 * Construct an effective image prompt from scene visuals
 * @param {Object} sceneVisuals - The scene visuals object
 * @returns {string} The constructed prompt
 */
function constructImagePrompt(sceneVisuals) {
  const {
    background_description,
    character_state,
    scene_mood
  } = sceneVisuals;

  // Start with book illustration context
  let prompt = 'A detailed book illustration depicting ';

  // Add character state as the main subject
  if (character_state) {
    prompt += character_state.toLowerCase();
  } else {
    prompt += 'a character';
  }

  // Add background/setting description
  if (background_description) {
    prompt += ` in ${background_description.toLowerCase()}`;
  }

  // Add mood and atmosphere with more descriptive language
  if (scene_mood) {
    prompt += `. The scene has ${scene_mood.toLowerCase()}`;
  }

  // Add book illustration style and quality enhancers
  prompt += '. Style: professional book illustration, storybook art, detailed digital painting, rich colors, clear composition, literary artwork, fantasy book cover style, high quality, sharp details, artistic lighting, engaging visual storytelling';

  // Add negative elements to avoid
  prompt += '. Avoid: blurry details, unclear faces, distorted anatomy, low quality, watermarks, text overlays';

  return prompt;
}

/**
 * Generate multiple images for different screens of the scenario
 * @param {Object} scenarioData - The complete scenario data
 * @returns {Promise<Object>} The generated images for each screen
 */
export async function generateScenarioImages(scenarioData) {
  try {
    const { scene_visuals, screens } = scenarioData;

    // For now, we'll generate one main image based on scene_visuals
    const mainImage = await generateSceneImage(scene_visuals);

    if (!mainImage.success) {
      return {
        success: false,
        error: mainImage.error
      };
    }

    return {
      success: true,
      images: {
        main: mainImage.data,
        // You could add more images here for different screens
      }
    };
  } catch (error) {
    console.error('Error generating scenario images:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

/**
 * Create variations of the scene visuals for different screens
 * @param {Object} baseSceneVisuals - The base scene visuals
 * @param {Object} screen - The screen data
 * @returns {Object} Modified scene visuals for the specific screen
 */
function createScreenVariation(baseSceneVisuals, screen) {
  // This function could modify the scene visuals based on the screen content
  // For example, changing character expressions or adding elements mentioned in the text

  const variation = { ...baseSceneVisuals };

  // You could analyze the screen text and modify the visuals accordingly
  if (screen.text && screen.text.includes('surprised')) {
    variation.character_state = variation.character_state.replace(/calm|peaceful/, 'surprised, shocked');
  }

  if (screen.text && screen.text.includes('dark')) {
    variation.scene_mood = variation.scene_mood.replace(/bright|light/, 'dark, mysterious');
  }

  return variation;
}
  
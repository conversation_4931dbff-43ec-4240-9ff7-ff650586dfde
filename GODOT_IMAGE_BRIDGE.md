# Godot Image Loading Bridge

This document explains how the Godot game can access Firebase Storage images through the JavaScript bridge.

## Overview

Since the Godot game is compiled and cannot be modified directly, we've created a JavaScript bridge that handles Firebase Storage image loading and makes the images available to the Godot game.

## Available Global Objects

### 1. `window.GodotImageLoader`

A global service that provides image loading capabilities:

```javascript
// Load image as base64 data URL
const base64Image = await window.GodotImageLoader.loadImageAsBase64(imageUrl);

// Load image as object URL
const objectUrl = await window.GodotImageLoader.loadImageAsObjectURL(imageUrl);

// Preload the background image for the current story
const base64Image = await window.GodotImageLoader.preloadBackgroundImage();
```

### 2. `window.preloadedBackgroundImage`

Contains the preloaded background image as a base64 data URL. This is automatically populated when the game loads.

### 3. `window.storyGameData`

Contains the complete story data including the `backgroundImageUrl` field.

## How It Works

1. **Automatic Preloading**: When the game loads, the background image is automatically fetched from Firebase Storage and converted to base64.

2. **Caching**: Images are cached to avoid repeated downloads.

3. **Error Handling**: Failed image loads are logged and don't crash the game.

## Firebase Storage Structure

**Important**: Images are stored in **Firebase Storage**, not in the Firestore database.

### Storage Path Format
Images are stored in Firebase Storage at paths like:
```
background/{chatId}/{imageId}.jpg
```

### URL Structure
Firebase Storage URLs follow this pattern:
```
https://firebasestorage.googleapis.com/v0/b/{project-id}.firebasestorage.app/o/background%2F{chatId}%2F{imageId}.jpg?alt=media&token={access-token}
```

Example URL:
```
https://firebasestorage.googleapis.com/v0/b/the-money-tales.firebasestorage.app/o/background%2FZVfBOxPXrrKnO97tVhxM%2Fb88f5145-4d27-4d96-89a0-67c68db92e44.jpg?alt=media&token=75b1e1db-d335-4b30-bc74-3447e7e19e84
```

### Data Flow
1. **Image Generation**: FreepikService generates images
2. **Storage**: Images are uploaded to Firebase Storage at `background/{chatId}/{imageId}.jpg`
3. **Database**: Only the Firebase Storage URL is stored in Firestore at `chats/{chatId}`
4. **Retrieval**: The bridge fetches images directly from Firebase Storage using the stored URLs

## Godot Integration

### Option 1: Use Preloaded Image

The simplest approach is to use the preloaded image:

```gdscript
# In Godot (GDScript example)
func _ready():
    # Check if preloaded image is available
    var js_code = "window.preloadedBackgroundImage"
    var base64_image = JavaScript.eval(js_code)
    
    if base64_image:
        # Convert base64 to ImageTexture
        load_image_from_base64(base64_image)
```

### Option 2: Load Image On Demand

```gdscript
# In Godot (GDScript example)
func load_background_image():
    var js_code = """
    (async function() {
        try {
            const storyData = JSON.parse(localStorage.getItem('storyGameData') || '{}');
            if (storyData.backgroundImageUrl) {
                return await window.GodotImageLoader.loadImageAsBase64(storyData.backgroundImageUrl);
            }
            return null;
        } catch (error) {
            console.error('Error loading image:', error);
            return null;
        }
    })()
    """
    
    # This would need to be handled with JavaScript.eval and callbacks
    # due to async nature
```

### Option 3: Use HTTP Request in Godot

If the Godot game has HTTP request capabilities:

```gdscript
# In Godot (GDScript example)
func load_image_via_http():
    # Get the image URL from story data
    var js_code = """
    (function() {
        const storyData = JSON.parse(localStorage.getItem('storyGameData') || '{}');
        return storyData.backgroundImageUrl || '';
    })()
    """
    var image_url = JavaScript.eval(js_code)
    
    if image_url:
        # Use HTTPRequest node to fetch the image
        var http_request = HTTPRequest.new()
        add_child(http_request)
        http_request.connect("request_completed", self, "_on_image_loaded")
        http_request.request(image_url)

func _on_image_loaded(result: int, response_code: int, headers: PoolStringArray, body: PoolByteArray):
    if response_code == 200:
        var image = Image.new()
        var error = image.load_jpg_from_buffer(body)
        if error == OK:
            var texture = ImageTexture.new()
            texture.create_from_image(image)
            # Use the texture for background
```

## Testing

The web interface provides test buttons to verify image loading:

1. **Test Image**: Tests basic Firebase Storage image fetching
2. **Test Bridge**: Tests the Godot Image Bridge functionality
3. **Debug**: Shows current story data and Godot integration status

## Troubleshooting

### Common Issues

1. **CORS Errors**: Firebase Storage should allow cross-origin requests, but check browser console for CORS issues.

2. **Image Not Found**: Verify the `backgroundImageUrl` exists in the story data.

3. **Bridge Not Available**: Ensure `window.GodotImageLoader` exists before using it.

4. **Timeout Issues**: Default timeout is 10 seconds. Check network connectivity if images fail to load.

### Browser Compatibility

The bridge uses universal JavaScript features for maximum compatibility:

- **Timeout Handling**: Uses Promise.race() with setTimeout (works in all modern browsers)
- **Fetch API**: Standard fetch() for HTTP requests (widely supported)
- **No AbortController**: Avoids compatibility issues by using Promise.race() for timeouts

Supported browsers:
- ✅ Chrome 42+ (fetch API support)
- ✅ Firefox 39+ (fetch API support)
- ✅ Safari 10.1+ (fetch API support)
- ✅ Edge 14+ (fetch API support)
- ✅ All modern browsers with fetch() support

### Debug Commands

Open browser console and run:

```javascript
// Check if bridge is available
console.log('Bridge available:', !!window.GodotImageLoader);

// Check preloaded image
console.log('Preloaded image:', !!window.preloadedBackgroundImage);

// Check story data
console.log('Story data:', window.storyGameData);

// Test image loading
window.GodotImageLoader.preloadBackgroundImage().then(console.log);
```

## Implementation Status

✅ **Completed**:
- JavaScript bridge for image loading
- Automatic image preloading
- Caching system with retry logic
- Comprehensive error handling
- Fallback image generation
- Test interface with multiple test buttons
- URL validation
- Status reporting
- Timeout handling (10 seconds)
- Exponential backoff retry strategy

⚠️ **Needs Godot Implementation**:
- Godot script to use the preloaded images
- HTTP request handling in Godot (if needed)
- Image texture creation from base64/binary data

## Testing the Implementation

1. **Start the development server**: `npm run dev`
2. **Navigate to a scenario page**: Go to `/alternate-scenario` with a valid chat ID
3. **Use the test buttons**:
   - **Test Image**: Tests basic Firebase Storage image fetching
   - **Test Bridge**: Tests the Godot Image Bridge functionality
   - **Status**: Shows comprehensive status information
   - **Debug**: Shows story data and Godot integration status

## Current Status

The bridge is fully functional and ready. The Firebase Storage image fetching has been tested and works correctly. The implementation includes:

- ✅ Successful image fetching from Firebase Storage URLs
- ✅ Proper CORS handling
- ✅ Base64 conversion for Godot compatibility
- ✅ Automatic fallback image generation
- ✅ Comprehensive error handling and retry logic
- ✅ Image caching to avoid repeated downloads
- ✅ Status monitoring and debugging tools

The Godot game needs to be updated to use either the preloaded images or the bridge functions to load images dynamically.

// FreepikService Test Script
// Run with: node tests/freepik-service-test.js
// This test verifies that the FreepikService is actually storing images to Firebase Storage

import { initializeApp } from 'firebase/app';
import { getStorage, ref, listAll, deleteObject, getDownloadURL } from 'firebase/storage';
import { getFirestore, collection, addDoc, deleteDoc, doc } from 'firebase/firestore';
import dotenv from 'dotenv';
import fs from 'fs';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Initialize Firebase with your config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log file setup
const logFile = './freepik-service-test.log';
fs.writeFileSync(logFile, `=== FreepikService Test Started at ${new Date().toISOString()} ===\n`);

// Custom logger that writes to console and file
const logger = {
  log: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.log(message);
    fs.appendFileSync(logFile, `[LOG] ${message}\n`);
  },
  error: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.error(message);
    fs.appendFileSync(logFile, `[ERROR] ${message}\n`);
  },
  success: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.log('✅', message);
    fs.appendFileSync(logFile, `[SUCCESS] ${message}\n`);
  },
  warn: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
    ).join(' ');
    console.warn('⚠️', message);
    fs.appendFileSync(logFile, `[WARN] ${message}\n`);
  }
};

// Initialize Firebase
logger.log('Initializing Firebase with config:', {
  apiKey: firebaseConfig.apiKey?.substring(0, 5) + '...',
  authDomain: firebaseConfig.authDomain,
  projectId: firebaseConfig.projectId,
  storageBucket: firebaseConfig.storageBucket
});

const app = initializeApp(firebaseConfig);
const storage = getStorage(app);
const db = getFirestore(app);

// Test data
const testSceneVisuals = {
  background_description: "A mystical forest with ancient trees and glowing mushrooms",
  character_state: "A young wizard standing confidently with a magical staff",
  scene_mood: "mysterious and enchanting atmosphere with soft magical lighting"
};

const testScenarioData = {
  scene_visuals: testSceneVisuals,
  screens: [
    {
      text: "The wizard discovers a hidden path through the enchanted forest.",
      choices: ["Follow the path", "Investigate the glowing mushrooms"]
    }
  ]
};

// Store test artifacts for cleanup
const testArtifacts = {
  storageFiles: [],
  firestoreDocuments: []
};

/**
 * Test the generateSceneImage function
 */
async function testGenerateSceneImage() {
  logger.log('\n=== Testing generateSceneImage Function ===');
  
  try {
    // Make sure the development server is running
    logger.log('Testing if development server is running...');
    const healthCheck = await fetch('http://localhost:3000/api/health', {
      method: 'GET'
    }).catch(() => null);
    
    if (!healthCheck || !healthCheck.ok) {
      logger.warn('Development server may not be running. Starting test anyway...');
    }

    logger.log('Calling generateSceneImage with test data...');
    logger.log('Test scene visuals:', testSceneVisuals);
    
    // Call the API endpoint directly since we can't import the client-side module
    const response = await fetch('http://localhost:3000/api/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: 'Test prompt for FreepikService test',
        sceneVisuals: testSceneVisuals
      }),
    });

    logger.log('API Response status:', response.status);
    logger.log('API Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      logger.error('API call failed:', errorText);
      return { success: false, error: `API call failed: ${response.status} - ${errorText}` };
    }

    const result = await response.json();
    logger.log('API Response result:', {
      success: result.success,
      hasData: !!result.data,
      hasBase64: !!(result.data?.base64),
      hasFirebaseUrl: !!(result.data?.firebaseUrl),
      error: result.error
    });

    if (result.success && result.data?.firebaseUrl) {
      logger.success('Image generation successful with Firebase storage!');
      logger.log('Firebase URL:', result.data.firebaseUrl);
      
      // Extract the file path from the URL for cleanup
      const urlParts = result.data.firebaseUrl.split('/');
      const filePathWithToken = urlParts[urlParts.length - 1];
      const filePath = filePathWithToken.split('?')[0]; // Remove query parameters
      testArtifacts.storageFiles.push(decodeURIComponent(filePath));
      
      return { success: true, result, firebaseUrl: result.data.firebaseUrl };
    } else if (result.success && result.data?.base64) {
      logger.warn('Image generation successful but no Firebase URL found');
      return { success: true, result, warning: 'No Firebase storage' };
    } else {
      logger.error('Image generation failed:', result.error);
      return { success: false, error: result.error };
    }
    
  } catch (error) {
    logger.error('Error in testGenerateSceneImage:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test Firebase Storage verification
 */
async function testFirebaseStorageVerification(firebaseUrl) {
  logger.log('\n=== Testing Firebase Storage Verification ===');
  
  try {
    if (!firebaseUrl) {
      logger.warn('No Firebase URL provided for verification');
      return { success: false, error: 'No Firebase URL to verify' };
    }

    logger.log('Verifying Firebase Storage URL:', firebaseUrl);
    
    // Test if the URL is accessible
    const response = await fetch(firebaseUrl, { method: 'HEAD' });
    logger.log('Storage URL response status:', response.status);
    logger.log('Storage URL response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      logger.success('Firebase Storage URL is accessible!');
      
      // Get content type and size information
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      
      logger.log('Content Type:', contentType);
      logger.log('Content Length:', contentLength, 'bytes');
      
      return { 
        success: true, 
        accessible: true,
        contentType,
        contentLength: parseInt(contentLength) || 0
      };
    } else {
      logger.error('Firebase Storage URL is not accessible');
      return { success: false, error: `URL not accessible: ${response.status}` };
    }
    
  } catch (error) {
    logger.error('Error verifying Firebase Storage:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test listing files in Firebase Storage background folder
 */
async function testListStorageFiles() {
  logger.log('\n=== Testing Firebase Storage File Listing ===');
  
  try {
    const backgroundRef = ref(storage, 'background/');
    logger.log('Listing files in background/ folder...');
    
    const listResult = await listAll(backgroundRef);
    logger.log('Found', listResult.items.length, 'files in background/ folder');
    
    if (listResult.items.length > 0) {
      logger.log('Files found:');
      for (const itemRef of listResult.items) {
        logger.log('- ', itemRef.name);
        
        // Get download URL for verification
        try {
          const downloadUrl = await getDownloadURL(itemRef);
          logger.log('  Download URL:', downloadUrl.substring(0, 50) + '...');
        } catch (urlError) {
          logger.error('  Error getting download URL:', urlError.message);
        }
      }
      
      logger.success('Successfully listed storage files!');
      return { success: true, fileCount: listResult.items.length, files: listResult.items };
    } else {
      logger.warn('No files found in background/ folder');
      return { success: true, fileCount: 0, files: [] };
    }
    
  } catch (error) {
    logger.error('Error listing storage files:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test cleanup of test artifacts
 */
async function cleanupTestArtifacts() {
  logger.log('\n=== Cleaning Up Test Artifacts ===');

  let cleanupResults = {
    storageFiles: { success: 0, failed: 0 },
    firestoreDocuments: { success: 0, failed: 0 }
  };

  // Clean up storage files
  if (testArtifacts.storageFiles.length > 0) {
    logger.log('Cleaning up', testArtifacts.storageFiles.length, 'storage files...');

    for (const filePath of testArtifacts.storageFiles) {
      try {
        const fileRef = ref(storage, filePath);
        await deleteObject(fileRef);
        logger.log('Deleted storage file:', filePath);
        cleanupResults.storageFiles.success++;
      } catch (error) {
        logger.error('Failed to delete storage file:', filePath, error.message);
        cleanupResults.storageFiles.failed++;
      }
    }
  }

  // Clean up Firestore documents
  if (testArtifacts.firestoreDocuments.length > 0) {
    logger.log('Cleaning up', testArtifacts.firestoreDocuments.length, 'Firestore documents...');

    for (const docRef of testArtifacts.firestoreDocuments) {
      try {
        await deleteDoc(docRef);
        logger.log('Deleted Firestore document:', docRef.path);
        cleanupResults.firestoreDocuments.success++;
      } catch (error) {
        logger.error('Failed to delete Firestore document:', docRef.path, error.message);
        cleanupResults.firestoreDocuments.failed++;
      }
    }
  }

  logger.log('Cleanup results:', cleanupResults);
  return cleanupResults;
}

/**
 * Test error handling scenarios
 */
async function testErrorHandling() {
  logger.log('\n=== Testing Error Handling ===');

  try {
    // Test with invalid scene visuals
    logger.log('Testing with invalid scene visuals...');

    const invalidResponse = await fetch('http://localhost:3000/api/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt: '',
        sceneVisuals: null
      }),
    });

    const invalidResult = await invalidResponse.json();
    logger.log('Invalid input response:', {
      success: invalidResult.success,
      error: invalidResult.error
    });

    // Test with malformed request
    logger.log('Testing with malformed request...');

    const malformedResponse = await fetch('http://localhost:3000/api/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: 'invalid json',
    });

    logger.log('Malformed request status:', malformedResponse.status);

    return { success: true, message: 'Error handling tests completed' };

  } catch (error) {
    logger.error('Error in testErrorHandling:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test the complete workflow
 */
async function testCompleteWorkflow() {
  logger.log('\n=== Testing Complete Workflow ===');

  try {
    // Step 1: Generate image
    const imageResult = await testGenerateSceneImage();
    if (!imageResult.success) {
      return { success: false, error: 'Image generation failed', details: imageResult };
    }

    // Step 2: Verify Firebase storage
    let storageResult = null;
    if (imageResult.firebaseUrl) {
      storageResult = await testFirebaseStorageVerification(imageResult.firebaseUrl);
    }

    // Step 3: List storage files
    const listResult = await testListStorageFiles();

    // Step 4: Test error handling
    const errorResult = await testErrorHandling();

    const workflowResults = {
      imageGeneration: imageResult,
      storageVerification: storageResult,
      fileListing: listResult,
      errorHandling: errorResult
    };

    logger.log('Complete workflow results:', workflowResults);

    // Determine overall success
    const overallSuccess = imageResult.success &&
                          (storageResult ? storageResult.success : true) &&
                          listResult.success &&
                          errorResult.success;

    if (overallSuccess) {
      logger.success('Complete workflow test PASSED!');
    } else {
      logger.error('Complete workflow test FAILED!');
    }

    return { success: overallSuccess, results: workflowResults };

  } catch (error) {
    logger.error('Error in testCompleteWorkflow:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Main test runner
 */
async function runTests() {
  logger.log('Starting FreepikService tests...');
  logger.log('Test configuration:', {
    firebaseProject: firebaseConfig.projectId,
    storageBucket: firebaseConfig.storageBucket,
    testDataKeys: Object.keys(testSceneVisuals)
  });

  const testResults = {
    startTime: new Date().toISOString(),
    tests: {},
    summary: {}
  };

  try {
    // Run the complete workflow test
    testResults.tests.completeWorkflow = await testCompleteWorkflow();

    // Calculate summary
    const allTests = Object.values(testResults.tests);
    testResults.summary = {
      total: allTests.length,
      passed: allTests.filter(test => test.success).length,
      failed: allTests.filter(test => !test.success).length,
      endTime: new Date().toISOString()
    };

    logger.log('\n=== TEST SUMMARY ===');
    logger.log('Total tests:', testResults.summary.total);
    logger.log('Passed:', testResults.summary.passed);
    logger.log('Failed:', testResults.summary.failed);

    if (testResults.summary.failed === 0) {
      logger.success('🎉 ALL TESTS PASSED! FreepikService is working correctly.');
    } else {
      logger.error('❌ Some tests failed. Check the logs for details.');
    }

    // Clean up test artifacts
    await cleanupTestArtifacts();

    logger.log('\n=== Tests Completed ===');
    logger.log('Full results saved to:', logFile);

  } catch (error) {
    logger.error('Unhandled error in test runner:', error);
    testResults.error = error.message;
  }

  return testResults;
}

// Run the tests
runTests().catch(error => {
  logger.error('Unhandled error in test script:', error);
  process.exit(1);
});

'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getBestImageSource } from '../utils/imageUtils';

/**
 * Fallback component to display game images when Godot game fails to load them
 * This component shows the background images that should be displayed in the game
 */
export default function GameImageFallback({ gameData, isVisible = false }) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Get the current question with image data
  const currentQuestion = gameData?.questions?.[currentQuestionIndex];
  const hasImage = currentQuestion && (currentQuestion.imageUrl || currentQuestion.imageBase64 || currentQuestion.image);

  // Get the best image source
  const imageSrc = hasImage ? getBestImageSource(currentQuestion) : null;

  useEffect(() => {
    setImageError(false);
    setImageLoading(true);
  }, [currentQuestionIndex, imageSrc]);

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const nextQuestion = () => {
    if (gameData?.questions && currentQuestionIndex < gameData.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const prevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  if (!isVisible || !gameData?.questions?.length) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
        {/* Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between">
          <h2 className="text-white text-xl font-bold">Game Images Debug View</h2>
          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm">
              Question {currentQuestionIndex + 1} of {gameData.questions.length}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Question Info */}
          <div className="mb-6">
            <h3 className="text-white text-lg font-semibold mb-2">
              {currentQuestion?.title || `Question ${currentQuestionIndex + 1}`}
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Node ID: {currentQuestion?.node_id} | Level: {currentQuestion?.level}
            </p>
          </div>

          {/* Image Display */}
          {hasImage ? (
            <div className="mb-6">
              <h4 className="text-white text-md font-medium mb-3">Background Image</h4>
              
              {/* Image Info */}
              <div className="mb-4 p-3 bg-gray-800 rounded text-sm">
                <div className="grid grid-cols-2 gap-2 text-gray-300">
                  <div>
                    <strong>Firebase URL:</strong> {currentQuestion.imageUrl ? '✓' : '✗'}
                  </div>
                  <div>
                    <strong>Base64 Data:</strong> {currentQuestion.imageBase64 ? '✓' : '✗'}
                  </div>
                  <div>
                    <strong>Source:</strong> {currentQuestion.imageSource || 'unknown'}
                  </div>
                  <div>
                    <strong>Image ID:</strong> {currentQuestion.image?.imageId || 'N/A'}
                  </div>
                </div>
                
                {currentQuestion.imageUrl && (
                  <div className="mt-2">
                    <strong className="text-gray-300">URL:</strong>
                    <div className="text-xs text-blue-400 break-all mt-1">
                      {currentQuestion.imageUrl}
                    </div>
                  </div>
                )}
              </div>

              {/* Image Display */}
              {imageSrc ? (
                <div className="relative">
                  {imageLoading && (
                    <div className="absolute inset-0 bg-gray-700/50 rounded-lg flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    </div>
                  )}
                  
                  <Image
                    src={imageSrc}
                    alt={`Background for ${currentQuestion.title}`}
                    width={800}
                    height={400}
                    className="w-full h-auto rounded-lg shadow-lg"
                    style={{ maxHeight: '400px', objectFit: 'cover' }}
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                    priority
                  />
                  
                  {imageError && (
                    <div className="absolute inset-0 bg-red-900/50 rounded-lg flex items-center justify-center">
                      <div className="text-center text-white">
                        <p className="text-lg mb-2">Failed to load image</p>
                        <p className="text-sm text-red-200">
                          {currentQuestion.imageUrl ? 'Firebase URL failed' : 'Base64 data invalid'}
                        </p>
                      </div>
                    </div>
                  )}
                  
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    Generated by Freepik AI
                  </div>
                </div>
              ) : (
                <div className="bg-gray-700/50 rounded-lg flex items-center justify-center p-8">
                  <p className="text-gray-400">No image source available</p>
                </div>
              )}
            </div>
          ) : (
            <div className="mb-6">
              <h4 className="text-white text-md font-medium mb-3">Background Image</h4>
              <div className="bg-gray-700/50 rounded-lg flex items-center justify-center p-8">
                <p className="text-gray-400">No image data for this question</p>
              </div>
            </div>
          )}

          {/* Question Text Preview */}
          <div className="mb-6">
            <h4 className="text-white text-md font-medium mb-3">Question Text</h4>
            <div className="bg-gray-800 rounded p-4 text-gray-300 text-sm max-h-32 overflow-y-auto">
              {currentQuestion?.question || 'No question text available'}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="p-4 border-t border-gray-700 flex items-center justify-between">
          <button
            onClick={prevQuestion}
            disabled={currentQuestionIndex === 0}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <div className="flex space-x-2">
            {gameData.questions.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentQuestionIndex(index)}
                className={`w-3 h-3 rounded-full ${
                  index === currentQuestionIndex ? 'bg-blue-500' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
          
          <button
            onClick={nextQuestion}
            disabled={currentQuestionIndex === gameData.questions.length - 1}
            className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
